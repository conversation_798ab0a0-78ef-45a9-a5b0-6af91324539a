package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.*;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import com.sankuai.deepcode.ast.model.base.LocationInfo;

/**
 * <AUTHOR>
 */
public class PythonModuleVisitor extends PythonParserBaseVisitor<Object> {
    private final PythonAnalysesResult result;
    private final PythonModuleNode moduleNode;
    private final ScopeManager scopeManager;

    public PythonModuleVisitor(PythonModuleNode moduleNode, PythonAnalysesResult result) {
        this.result = result;
        this.moduleNode = moduleNode;

        result.getScopeManagerMap().computeIfAbsent(moduleNode.getModulePath(), k -> new ScopeManager(moduleNode.getModuleName()));
        this.scopeManager = result.getScopeManagerMap().get(moduleNode.getModulePath());
    }

    @Override
    public Object visitFile_input(PythonParser.File_inputContext ctx) {
        // 处理模块的文档字符串和块级注释
        if (ctx.statements() != null) {
            processModuleDocstringAndComments(ctx.statements());
        }
        return super.visitFile_input(ctx);
    }


    @Override
    public Object visitAssignment(PythonParser.AssignmentContext ctx) {
        PythonAssignmentVisitor assignmentVisitor = new PythonAssignmentVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        assignmentVisitor.visit(ctx);
        PythonParameterNode paramNode = assignmentVisitor.getParamNode();
        moduleNode.getModuleParamNodes().add(paramNode);

        return super.visitAssignment(ctx);
    }


    @Override
    public Object visitClass_def(PythonParser.Class_defContext ctx) {
        PythonClassVisitor classVisitor = new PythonClassVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        classVisitor.visit(ctx);
        PythonClassNode classNode = classVisitor.getClassNode();
        scopeManager.addToScope(classNode.getName(), moduleNode.getModulePath() + "." + classNode.getName(), ctx.getStart().getLine());
        moduleNode.getClassNodes().add(classNode);
        return null;
    }

    @Override
    public Object visitImport_stmt(PythonParser.Import_stmtContext ctx) {
        PythonImportVisitor importVisitor = new PythonImportVisitor(moduleNode.getFileName(), moduleNode.getModuleName(), moduleNode.getModulePath(), scopeManager);
        importVisitor.visitImport_stmt(ctx);
        moduleNode.getImportNodes().addAll(importVisitor.getImportNodes());
        return super.visitImport_stmt(ctx);
    }

    @Override
    public Object visitFunction_def(PythonParser.Function_defContext ctx) {
        PythonFunctionDefVisitor functionVisitor = new PythonFunctionDefVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        functionVisitor.visit(ctx);
        scopeManager.addToScope(functionVisitor.getFunctionNode().getName(), moduleNode.getModulePath() + "." + functionVisitor.getFunctionNode().getName(), ctx.getStart().getLine());
        moduleNode.getFunctionNodes().add(functionVisitor.getFunctionNode());
        return null;
    }

    @Override
    public Object visitFunction_call(PythonParser.Function_callContext ctx) {
        // 使用统一的函数调用处理器
        PythonFunctionCallHandler handler = new PythonFunctionCallHandler(
            moduleNode.getFileName(),
            moduleNode.getModulePath(),
            scopeManager
        );
        moduleNode.getFunctionCallNodes().addAll(handler.handleFunctionCall(ctx));
        // 返回 null，不再进行子节点的访问，只访问到当前层的节点方法调用
        return null;
    }

    @Override
    public Object visitPrimary(PythonParser.PrimaryContext ctx) {
        // 检查是否包含函数调用（即包含 '(' arguments? ')' 的primary_suffix）
        boolean hasFunctionCall = false;
        for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
            if (suffix.LPAR() != null) {
                hasFunctionCall = true;
                break;
            }
        }

        if (hasFunctionCall) {
            // 传入当前模块所在的路径，用于解析函数调用的路径
            PythonFunctionCallVisitor functionCallVisitor = new PythonFunctionCallVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
            functionCallVisitor.visitPrimary(ctx);
            moduleNode.getFunctionCallNodes().addAll(functionCallVisitor.getFunctionCallNodes());
        }

        return super.visitPrimary(ctx);
    }

    @Override
    public Object visitLambdef(PythonParser.LambdefContext ctx) {
        // 解析lambda表达式中的函数调用
        parseLambdaExpressionFunctionCalls(ctx);
        return null;
    }

    /**
     * 解析lambda表达式中的函数调用
     */
    private void parseLambdaExpressionFunctionCalls(PythonParser.LambdefContext ctx) {
        // 创建一个visitor来遍历lambda表达式中的函数调用
        PythonParserBaseVisitor<Void> lambdaExpressionVisitor = new PythonParserBaseVisitor<Void>() {
            @Override
            public Void visitFunction_call(PythonParser.Function_callContext functionCallCtx) {
                // 使用统一的函数调用处理器
                PythonFunctionCallHandler handler = new PythonFunctionCallHandler(
                    moduleNode.getFileName(),
                    moduleNode.getModulePath(),
                    scopeManager
                );
                moduleNode.getFunctionCallNodes().addAll(handler.handleFunctionCall(functionCallCtx));
                return null;
            }

            @Override
            public Void visitPrimary(PythonParser.PrimaryContext primaryCtx) {
                // 检查是否包含函数调用
                boolean hasFunctionCall = false;
                for (PythonParser.Primary_suffixContext suffix : primaryCtx.primary_suffix()) {
                    if (suffix.LPAR() != null) {
                        hasFunctionCall = true;
                        break;
                    }
                }

                if (hasFunctionCall) {
                    PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(
                        moduleNode.getFileName(),
                        moduleNode.getModulePath(),
                        scopeManager
                    );
                    callVisitor.visitPrimary(primaryCtx);
                    moduleNode.getFunctionCallNodes().addAll(callVisitor.getFunctionCallNodes());
                }

                return super.visitPrimary(primaryCtx);
            }
        };

        // 访问lambda表达式的body
        if (ctx.expression() != null) {
            ctx.expression().accept(lambdaExpressionVisitor);
        }
    }


    @Override
    public Object visitDel_stmt(PythonParser.Del_stmtContext ctx) {
        PythonDelVisitor delVisitor = new PythonDelVisitor(moduleNode.getFileName(), moduleNode.getModuleName(), moduleNode.getModulePath(), scopeManager);
        delVisitor.visitDel_stmt(ctx);
        return super.visitDel_stmt(ctx);
    }

    /**
     * 处理模块的文档字符串和其他块级注释
     * 第一个三引号字符串作为模块的文档字符串，其他的作为其他块级注释
     */
    private void processModuleDocstringAndComments(PythonParser.StatementsContext statementsCtx) {
        if (statementsCtx == null) {
            return;
        }

        boolean foundFirstDocstring = false;

        for (PythonParser.StatementContext stmt : statementsCtx.statement()) {
            // 检查是否是简单语句且只包含一个表达式（可能是字符串字面量）
            if (stmt.simple_stmts() != null && stmt.simple_stmts().simple_stmt().size() == 1) {
                PythonParser.Simple_stmtContext simpleStmt = stmt.simple_stmts().simple_stmt(0);

                // 检查是否是表达式语句
                if (simpleStmt.star_expressions() != null) {
                    String possibleDocstring = extractStringLiteral(simpleStmt.star_expressions());

                    if (possibleDocstring != null && isMultilineString(possibleDocstring)) {
                        PythonBlockComment blockComment = new PythonBlockComment()
                                .setStart(LocationInfo.of(stmt.getStart().getLine()))
                                .setEnd(LocationInfo.of(stmt.getStop().getLine()))
                                .setComment(cleanDocstring(possibleDocstring))
                                .setBlockModulePath(moduleNode.getModulePath());

                        if (!foundFirstDocstring) {
                            // 第一个三引号字符串作为模块的文档字符串
                            moduleNode.setModuleComment(blockComment);
                            foundFirstDocstring = true;
                        } else {
                            // 其他的作为其他块级注释
                            moduleNode.getOtherBlockComments().add(blockComment);
                        }
                    }
                }
            }
        }
    }

    /**
     * 从表达式中提取字符串字面量
     */
    private String extractStringLiteral(PythonParser.Star_expressionsContext ctx) {
        // 简化的字符串提取逻辑，实际可能需要更复杂的解析
        String text = ctx.getText();
        if (text.startsWith("\"\"\"") || text.startsWith("'''")) {
            return text;
        }
        return null;
    }

    /**
     * 清理文档字符串，移除三引号
     */
    private String cleanDocstring(String docstring) {
        if (docstring.startsWith("'''") && docstring.endsWith("'''")) {
            return docstring.substring(3, docstring.length() - 3).trim();
        } else if (docstring.startsWith("\"\"\"") && docstring.endsWith("\"\"\"")) {
            return docstring.substring(3, docstring.length() - 3).trim();
        }
        return docstring;
    }

    private boolean isMultilineString(String str) {
        return str.startsWith("'''") || str.startsWith("\"\"\"");
    }

    public PythonAnalysesResult getResult() {
        return result;
    }

    public PythonModuleNode getModuleNode() {
        return moduleNode;
    }

    public ScopeManager getScopeManager() {
        return scopeManager;
    }
}
