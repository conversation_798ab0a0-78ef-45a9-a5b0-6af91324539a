package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class PythonFunctionCallVisitor extends PythonParserBaseVisitor<Object> {
    @Setter
    private String fileName;
    private final String modulePath;
    private final ScopeManager scopeManager;
    private final List<PythonFunctionCallNode> functionCallNodes = new ArrayList<>();

    public PythonFunctionCallVisitor(String fileName, String modulePath, ScopeManager scopeManager) {
        this.fileName = fileName;
        this.modulePath = modulePath;
        this.scopeManager = scopeManager;
    }

    @Override
    public Object visitFunction_call(PythonParser.Function_callContext ctx) {
        // 新的function_call规则指向primary_call_chain
        if (ctx.primary_call_chain() != null) {
            processPrimaryCallChain(ctx.primary_call_chain());
        }
        // 返回null，不再继续访问子节点，因为我们已经在processPrimaryCallChain中处理了
        return null;
    }

    @Override
    public Object visitPrimary(PythonParser.PrimaryContext ctx) {
        // 检查是否包含函数调用（即包含 '(' arguments? ')' 的primary_suffix）
        boolean hasFunctionCall = false;
        for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
            if (suffix.LPAR() != null) {
                hasFunctionCall = true;
                break;
            }
        }

        if (hasFunctionCall) {
            processPrimaryAsFunctionCall(ctx);
        }

        // 不调用 super.visitPrimary(ctx) 来避免重复处理嵌套的函数调用
        return null;
    }

    private void processPrimaryAsFunctionCall(PythonParser.PrimaryContext ctx) {
        // 修复位置信息
        LocationInfo startLocation = new LocationInfo(ctx.start.getLine(), ctx.start.getCharPositionInLine());
        LocationInfo endLocation = new LocationInfo(ctx.stop.getLine(), ctx.stop.getCharPositionInLine());

        StringBuilder fullCallChain = new StringBuilder();
        StringBuilder currentFunctionName = new StringBuilder();
        String baseModulePath = null;

        // 处理atom部分
        if (ctx.atom() != null) {
            String atomText = getNodeText(ctx.atom());
            fullCallChain.append(atomText);
            currentFunctionName.append(atomText);

            // 只在atom是NAME时解析模块路径
            if (ctx.atom().NAME() != null) {
                // 使用当前行号进行作用域解析，而不是固定的1
                int currentLine = ctx.start.getLine();
                baseModulePath = scopeManager.resolveSymbol(atomText, currentLine);
            }
        }

        // 处理primary_suffix部分，为每个函数调用创建单独的节点
        List<PythonParser.Primary_suffixContext> suffixes = ctx.primary_suffix();

        for (PythonParser.Primary_suffixContext suffix : suffixes) {
            if (suffix.DOT() != null && suffix.NAME() != null) {
                // 属性访问 '.' NAME
                String methodName = getNodeText(suffix.NAME());
                fullCallChain.append(".").append(methodName);
                currentFunctionName.append(".").append(methodName);
            } else if (suffix.LPAR() != null) {
                // 函数调用 '(' arguments? ')'
                // 为当前函数调用创建一个节点
                PythonFunctionCallNode functionCallNode = new PythonFunctionCallNode();
                functionCallNode.setStart(startLocation);
                functionCallNode.setEnd(endLocation);
                functionCallNode.setModuleName(modulePath);
                functionCallNode.setFilePath(fileName);

                // 构建完整的调用代码
                StringBuilder callCode = new StringBuilder(fullCallChain);
                callCode.append("(");
                if (suffix.arguments() != null) {
                    processArguments(suffix.arguments(), functionCallNode, callCode);
                }
                callCode.append(")");

                // 设置函数名 - 修复：对于方法调用，只保留方法名
                String functionNameStr = currentFunctionName.toString();

                // 对于方法调用（如datetime.now），只保留方法名部分
                if (functionNameStr.contains(".")) {
                    String[] parts = functionNameStr.split("\\.");
                    functionNameStr = parts[parts.length - 1]; // 只保留最后一部分（方法名）
                }

                // 修正super调用的函数名格式
                if (functionNameStr.startsWith("super.")) {
                    // 将 "super.__init__" 转换为 "super().__init__"
                    String methodName = functionNameStr.substring(6); // 去掉"super."
                    String correctedFunctionName = "super()." + methodName;
                    functionCallNode.setFunctionName(correctedFunctionName);
                } else {
                    functionCallNode.setFunctionName(functionNameStr);
                }

                // 设置模块路径和原始代码
                functionCallNode.setSourceModulePath(PythonFunctionCallUtils.calculateImprovedSourceModulePath(
                    currentFunctionName.toString(), baseModulePath, callCode.toString(), ctx.start.getLine(), scopeManager, modulePath));
                functionCallNode.setRawCode(callCode.toString());

                functionCallNodes.add(functionCallNode);

                // 更新fullCallChain以包含这个函数调用（为了处理链式调用）
                fullCallChain.append("(");
                if (suffix.arguments() != null) {
                    // 简单地添加参数占位符，不重复处理参数
                    fullCallChain.append("...");
                }
                fullCallChain.append(")");

                // 处理参数中的嵌套函数调用
                if (suffix.arguments() != null) {
                    processNestedFunctionCallsInArguments(suffix.arguments());
                }

            } else if (suffix.LSQB() != null) {
                // 索引访问 '[' slices ']'
                fullCallChain.append("[");
                if (suffix.slices() != null) {
                    fullCallChain.append(getNodeText(suffix.slices()));
                }
                fullCallChain.append("]");
            } else {
                // 其他类型的suffix
                fullCallChain.append(getNodeText(suffix));
            }
        }
    }

    private void processPrimaryCallChain(PythonParser.Primary_call_chainContext ctx) {
        PythonFunctionCallNode functionCallNode = new PythonFunctionCallNode();
        functionCallNode.setStart(new LocationInfo(ctx.start.getLine(), ctx.start.getCharPositionInLine()));
        functionCallNode.setEnd(new LocationInfo(ctx.stop.getLine(), ctx.stop.getCharPositionInLine()));
        functionCallNode.setModuleName(modulePath);
        functionCallNode.setFilePath(fileName);

        StringBuilder fullCallChain = new StringBuilder();
        StringBuilder functionName = new StringBuilder();
        String baseModulePath = null;

        // 处理atom部分
        if (ctx.atom() != null) {
            String atomText = ctx.atom().getText();
            fullCallChain.append(atomText);
            functionName.append(atomText);

            // 只在atom是NAME时解析模块路径
            if (ctx.atom().NAME() != null) {
                baseModulePath = scopeManager.resolveSymbol(atomText, 1);
            }
        }

        // 处理call_suffix部分
        List<PythonParser.Call_suffixContext> callSuffixes = ctx.call_suffix();
        for (PythonParser.Call_suffixContext suffix : callSuffixes) {
            processCallSuffix(suffix, functionCallNode, fullCallChain, functionName);
        }

        // 设置最终的函数名和模块路径
        String finalFunctionName = functionName.toString();

        // 修正super调用的函数名格式
        if (finalFunctionName.startsWith("super.")) {
            // 将 "super.__init__" 转换为 "super().__init__"
            String methodName = finalFunctionName.substring(6); // 去掉"super."
            finalFunctionName = "super()." + methodName;
        }

        functionCallNode.setFunctionName(finalFunctionName);
        functionCallNode.setSourceModulePath(PythonFunctionCallUtils.calculateImprovedSourceModulePath(
            finalFunctionName, baseModulePath, fullCallChain.toString(), ctx.start.getLine(), scopeManager, modulePath));
        functionCallNode.setRawCode(fullCallChain.toString());
        functionCallNodes.add(functionCallNode);
    }

    private void processCallSuffix(PythonParser.Call_suffixContext suffix, PythonFunctionCallNode functionCallNode,
                                   StringBuilder fullCallChain, StringBuilder functionName) {
        // 根据ANTLR生成的代码，Call_suffixContext有三种形式：
        // 1. '(' arguments? ')'
        // 2. '.' NAME '(' arguments? ')'
        // 3. '[' slices ']' '(' arguments? ')'

        if (suffix.DOT() != null && suffix.NAME() != null && suffix.LPAR() != null) {
            // 这是属性访问后的函数调用 '.' NAME '(' arguments? ')'
            String methodName = suffix.NAME().getText();
            fullCallChain.append(".").append(methodName).append("(");

            if (suffix.arguments() != null) {
                processArguments(suffix.arguments(), functionCallNode, fullCallChain);
            }
            fullCallChain.append(")");

            // 对于方法调用，函数名应该只是方法名，不包含对象名
            // 例如：datetime.now() -> functionName = "now", sourceModulePath = "datetime.datetime"
            functionName.setLength(0); // 清空之前的内容
            functionName.append(methodName); // 只保留方法名
        } else if (suffix.LSQB() != null && suffix.slices() != null && suffix.LPAR() != null) {
            // 这是索引访问后的函数调用 '[' slices ']' '(' arguments? ')'
            fullCallChain.append("[").append(suffix.slices().getText()).append("]").append("(");
            if (suffix.arguments() != null) {
                processArguments(suffix.arguments(), functionCallNode, fullCallChain);
            }
            fullCallChain.append(")");
            // 对于索引访问，我们不改变函数名
        } else if (suffix.LPAR() != null && suffix.DOT() == null && suffix.LSQB() == null) {
            // 这是一个简单的函数调用 '(' arguments? ')'
            fullCallChain.append("(");
            if (suffix.arguments() != null) {
                processArguments(suffix.arguments(), functionCallNode, fullCallChain);
            }
            fullCallChain.append(")");

            // 不在这里设置函数名，将在最后统一设置
        }
    }


    private void processArguments(PythonParser.ArgumentsContext ctx, PythonFunctionCallNode node, StringBuilder fullCallChain) {
        if (ctx.args() != null) {
            List<String> args = new ArrayList<>();
            for (PythonParser.Starred_expressionContext starredExpr : ctx.args().starred_expression()) {
                String exprText = getNodeText(starredExpr.expression());
                args.add("*" + exprText);
                fullCallChain.append("*").append(exprText).append(", ");
            }
            for (PythonParser.ExpressionContext expr : ctx.args().expression()) {
                String exprText = getNodeText(expr);
                args.add(exprText);
                fullCallChain.append(exprText).append(", ");
            }
            for (PythonParser.Assignment_expressionContext assignExpr : ctx.args().assignment_expression()) {
                String exprText = getNodeText(assignExpr);
                args.add(exprText);
                fullCallChain.append(exprText).append(", ");
            }
            if (ctx.args().kwargs() != null) {
                processKwargs(ctx.args().kwargs(), args, fullCallChain);
            }
            // Remove trailing comma and space
            if (fullCallChain.length() > 2 && fullCallChain.substring(fullCallChain.length() - 2).equals(", ")) {
                fullCallChain.setLength(fullCallChain.length() - 2);
            }
            node.setArguments(args);
        }
    }

    private void processKwargs(PythonParser.KwargsContext ctx, List<String> args, StringBuilder fullCallChain) {
        for (PythonParser.Kwarg_or_starredContext kwargCtx : ctx.kwarg_or_starred()) {
            if (kwargCtx.NAME() != null) {
                String arg = kwargCtx.NAME().getText() + "=" + kwargCtx.expression().getText();
                args.add(arg);
                fullCallChain.append(arg).append(", ");
            } else if (kwargCtx.starred_expression() != null) {
                String arg = "*" + kwargCtx.starred_expression().expression().getText();
                args.add(arg);
                fullCallChain.append(arg).append(", ");
            }
        }
        for (PythonParser.Kwarg_or_double_starredContext kwargCtx : ctx.kwarg_or_double_starred()) {
            if (kwargCtx.NAME() != null) {
                String arg = kwargCtx.NAME().getText() + "=" + kwargCtx.expression().getText();
                args.add(arg);
                fullCallChain.append(arg).append(", ");
            } else {
                String arg = "**" + kwargCtx.expression().getText();
                args.add(arg);
                fullCallChain.append(arg).append(", ");
            }
        }
    }

    /**
     * 处理参数中的嵌套函数调用
     */
    private void processNestedFunctionCallsInArguments(PythonParser.ArgumentsContext ctx) {
        if (ctx.args() != null) {
            // 处理普通表达式参数
            for (PythonParser.ExpressionContext expr : ctx.args().expression()) {
                processExpressionForFunctionCalls(expr);
            }

            // 处理赋值表达式参数
            for (PythonParser.Assignment_expressionContext assignExpr : ctx.args().assignment_expression()) {
                if (assignExpr.expression() != null) {
                    processExpressionForFunctionCalls(assignExpr.expression());
                }
            }

            // 处理starred表达式参数
            for (PythonParser.Starred_expressionContext starredExpr : ctx.args().starred_expression()) {
                if (starredExpr.expression() != null) {
                    processExpressionForFunctionCalls(starredExpr.expression());
                }
            }

            // 处理kwargs
            if (ctx.args().kwargs() != null) {
                processKwargsForFunctionCalls(ctx.args().kwargs());
            }
        }
    }

    /**
     * 处理表达式中的函数调用
     */
    private void processExpressionForFunctionCalls(PythonParser.ExpressionContext ctx) {
        if (ctx.function_call() != null) {
            // 递归处理嵌套的函数调用
            PythonFunctionCallVisitor nestedVisitor = new PythonFunctionCallVisitor(fileName, modulePath, scopeManager);
            nestedVisitor.visitFunction_call(ctx.function_call());
            functionCallNodes.addAll(nestedVisitor.functionCallNodes);
        }
    }

    /**
     * 处理kwargs中的函数调用
     */
    private void processKwargsForFunctionCalls(PythonParser.KwargsContext ctx) {
        for (PythonParser.Kwarg_or_starredContext kwargCtx : ctx.kwarg_or_starred()) {
            if (kwargCtx.expression() != null) {
                processExpressionForFunctionCalls(kwargCtx.expression());
            }
        }
        for (PythonParser.Kwarg_or_double_starredContext kwargCtx : ctx.kwarg_or_double_starred()) {
            if (kwargCtx.expression() != null) {
                processExpressionForFunctionCalls(kwargCtx.expression());
            }
        }
    }

    /**
     * 计算源模块路径
     */
    private String calculateSourceModulePath(String functionName, String baseModulePath, String rawCode, int currentLine) {
        if (functionName == null || functionName.isEmpty()) {
            return "Unknown";
        }

        // 处理特殊情况
        if (functionName.startsWith("super()")) {
            return "SUPER_CLASS_TO_BE_RESOLVED";
        }

        // 如果是简单函数名（没有点），检查是否是内置函数
        if (!functionName.contains(".")) {
            if (isBuiltinFunction(functionName)) {
                return "BUILTIN";
            }

            // 优先使用baseModulePath（从atom解析得到的）
            if (baseModulePath != null && !baseModulePath.equals("Unknown")) {
                return baseModulePath;
            }

            // 对于方法调用（如datetime.now()），从rawCode中提取对象名
            if (rawCode != null && rawCode.contains(".")) {
                String[] parts = rawCode.split("\\.");
                if (parts.length >= 2) {
                    String objectName = parts[0];
                    // 尝试从作用域管理器解析对象名，使用当前行号
                    String resolved = scopeManager.resolveSymbol(objectName, currentLine);
                    if (resolved != null && !resolved.equals("Unknown")) {
                        return resolved;
                    }
                }
            }

            // 尝试从作用域管理器解析函数名，使用当前行号
            String resolved = scopeManager.resolveSymbol(functionName, currentLine);
            if (resolved != null && !resolved.equals("Unknown")) {
                // 如果解析到的是完整路径（如collections.defaultdict），直接返回
                return resolved;
            }

            return "Unknown";
        }

        // 对于链式调用，提取基础模块路径
        String[] parts = functionName.split("\\.");
        if (parts.length > 1) {
            String baseName = parts[0];

            // 首先尝试使用传入的baseModulePath
            if (baseModulePath != null && !baseModulePath.equals("Unknown")) {
                return baseModulePath;
            }

            // 如果没有基础模块路径，尝试从作用域管理器解析基础名称，使用当前行号
            String resolved = scopeManager.resolveSymbol(baseName, currentLine);
            if (resolved != null && !resolved.equals("Unknown")) {
                return resolved;
            }

            // 如果还是解析不到，返回基础名称
            return baseName;
        }

        return baseModulePath != null ? baseModulePath : "Unknown";
    }

    /**
     * 检查是否是Python内置函数
     */
    private boolean isBuiltinFunction(String functionName) {
        // Python常见内置函数列表
        String[] builtinFunctions = {
                "print", "len", "str", "int", "float", "bool", "list", "dict", "tuple", "set",
                "range", "enumerate", "zip", "map", "filter", "sorted", "reversed", "sum",
                "min", "max", "abs", "round", "pow", "divmod", "isinstance", "issubclass",
                "hasattr", "getattr", "setattr", "delattr", "callable", "type", "id",
                "hash", "repr", "format", "input", "open", "exec", "eval", "compile",
                "globals", "locals", "vars", "dir", "help", "copyright", "credits", "license"
        };

        for (String builtin : builtinFunctions) {
            if (builtin.equals(functionName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取节点的文本内容
     */
    private String getNodeText(Object ctx) {
        if (ctx == null) {
            return "";
        }
        // 如果是ANTLR的上下文节点，调用getText()方法
        try {
            java.lang.reflect.Method getTextMethod = ctx.getClass().getMethod("getText");
            return (String) getTextMethod.invoke(ctx);
        } catch (Exception e) {
            // 如果没有getText方法，返回toString()
            return ctx.toString();
        }
    }
}
