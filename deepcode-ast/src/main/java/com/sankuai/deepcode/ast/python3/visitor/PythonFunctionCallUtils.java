package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import com.sankuai.deepcode.ast.model.python3.ScopeManager;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Python函数调用处理工具类
 * 提供公共的函数调用解析逻辑，避免在多个visitor中重复代码
 * 
 * <AUTHOR>
 * @since 2025/1/13
 */
public class PythonFunctionCallUtils {
    
    /**
     * 检查Primary上下文是否包含函数调用
     */
    public static boolean containsFunctionCall(PythonParser.PrimaryContext ctx) {
        if (ctx.primary_suffix() == null) {
            return false;
        }
        
        for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
            if (suffix.LPAR() != null) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理Primary上下文中的函数调用
     */
    public static List<PythonFunctionCallNode> processPrimaryFunctionCalls(
            PythonParser.PrimaryContext ctx, 
            String fileName, 
            String modulePath, 
            ScopeManager scopeManager) {
        
        if (!containsFunctionCall(ctx)) {
            return new ArrayList<>();
        }
        
        PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(fileName, modulePath, scopeManager);
        callVisitor.visitPrimary(ctx);
        return callVisitor.getFunctionCallNodes();
    }
    
    /**
     * 处理函数调用上下文
     */
    public static List<PythonFunctionCallNode> processFunctionCall(
            PythonParser.Function_callContext ctx,
            String fileName,
            String modulePath,
            ScopeManager scopeManager) {
        
        PythonFunctionCallHandler handler = new PythonFunctionCallHandler(fileName, modulePath, scopeManager);
        return handler.handleFunctionCall(ctx);
    }
    
    /**
     * 处理lambda表达式中的函数调用
     */
    public static List<PythonFunctionCallNode> processLambdaFunctionCalls(
            PythonParser.LambdefContext ctx,
            String fileName,
            String modulePath,
            ScopeManager scopeManager) {
        
        List<PythonFunctionCallNode> functionCalls = new ArrayList<>();
        
        // 创建一个visitor来遍历lambda表达式中的函数调用
        PythonParserBaseVisitor<Void> lambdaExpressionVisitor = new PythonParserBaseVisitor<Void>() {
            @Override
            public Void visitFunction_call(PythonParser.Function_callContext functionCallCtx) {
                functionCalls.addAll(processFunctionCall(functionCallCtx, fileName, modulePath, scopeManager));
                return null;
            }

            @Override
            public Void visitPrimary(PythonParser.PrimaryContext primaryCtx) {
                functionCalls.addAll(processPrimaryFunctionCalls(primaryCtx, fileName, modulePath, scopeManager));
                return super.visitPrimary(primaryCtx);
            }
        };

        // 访问lambda表达式的body
        if (ctx.expression() != null) {
            ctx.expression().accept(lambdaExpressionVisitor);
        }
        
        return functionCalls;
    }
    
    /**
     * 处理return语句中的函数调用
     */
    public static List<PythonFunctionCallNode> processReturnStatementFunctionCalls(
            PythonParser.Star_expressionsContext ctx,
            String fileName,
            String modulePath,
            ScopeManager scopeManager) {
        
        List<PythonFunctionCallNode> functionCalls = new ArrayList<>();
        
        // 创建一个visitor来遍历return表达式中的函数调用
        PythonParserBaseVisitor<Void> returnExpressionVisitor = new PythonParserBaseVisitor<Void>() {
            @Override
            public Void visitFunction_call(PythonParser.Function_callContext functionCallCtx) {
                functionCalls.addAll(processFunctionCall(functionCallCtx, fileName, modulePath, scopeManager));
                return null;
            }

            @Override
            public Void visitPrimary(PythonParser.PrimaryContext primaryCtx) {
                functionCalls.addAll(processPrimaryFunctionCalls(primaryCtx, fileName, modulePath, scopeManager));
                return super.visitPrimary(primaryCtx);
            }
        };

        // 访问return表达式
        ctx.accept(returnExpressionVisitor);
        
        return functionCalls;
    }
    
    /**
     * 改进的源模块路径计算
     * 支持类方法调用和原生类型方法调用
     */
    public static String calculateImprovedSourceModulePath(
            String functionName, 
            String baseModulePath, 
            String rawCode, 
            int currentLine,
            ScopeManager scopeManager,
            String currentModulePath) {
        
        if (functionName == null || functionName.isEmpty()) {
            return "Unknown";
        }

        // 处理特殊情况
        if (functionName.startsWith("super()")) {
            return "SUPER_CLASS_TO_BE_RESOLVED";
        }

        // 如果是简单函数名（没有点），检查是否是内置函数
        if (!functionName.contains(".")) {
            if (isBuiltinFunction(functionName)) {
                return "BUILTIN";
            }

            // 优先使用baseModulePath（从atom解析得到的）
            if (baseModulePath != null && !baseModulePath.equals("Unknown")) {
                return baseModulePath;
            }

            // 对于方法调用，从rawCode中提取对象名并解析其类型
            if (rawCode != null && rawCode.contains(".")) {
                return resolveMethodCallSourcePath(rawCode, currentLine, scopeManager, currentModulePath);
            }

            // 尝试从作用域管理器解析函数名
            String resolved = scopeManager.resolveSymbol(functionName, currentLine);
            if (resolved != null && !resolved.equals("Unknown")) {
                return resolved;
            }

            return "Unknown";
        }

        // 对于链式调用，提取基础模块路径
        String[] parts = functionName.split("\\.");
        if (parts.length > 1) {
            String baseName = parts[0];

            // 首先尝试使用传入的baseModulePath
            if (baseModulePath != null && !baseModulePath.equals("Unknown")) {
                return baseModulePath;
            }

            // 如果没有基础模块路径，尝试从作用域管理器解析基础名称
            String resolved = scopeManager.resolveSymbol(baseName, currentLine);
            if (resolved != null && !resolved.equals("Unknown")) {
                return resolved;
            }

            // 如果还是解析不到，返回基础名称
            return baseName;
        }

        return baseModulePath != null ? baseModulePath : "Unknown";
    }
    
    /**
     * 解析方法调用的源路径
     * 支持类方法调用和原生类型方法调用
     */
    private static String resolveMethodCallSourcePath(String rawCode, int currentLine, ScopeManager scopeManager, String currentModulePath) {
        // 提取调用对象部分
        int dotIndex = rawCode.indexOf('.');
        if (dotIndex == -1) {
            return "Unknown";
        }
        
        String objectPart = rawCode.substring(0, dotIndex).trim();
        
        // 检查是否是原生类型字面量
        String builtinType = detectBuiltinTypeLiteral(objectPart);
        if (builtinType != null) {
            return "BUILTIN." + builtinType;
        }
        
        // 尝试从作用域解析对象类型
        String resolved = scopeManager.resolveSymbol(objectPart, currentLine);
        if (resolved != null && !resolved.equals("Unknown")) {
            return resolved;
        }
        
        // 检查是否是类名（首字母大写）
        if (Character.isUpperCase(objectPart.charAt(0))) {
            // 可能是类的静态方法调用
            String classPath = scopeManager.resolveSymbol(objectPart, currentLine);
            if (classPath != null && !classPath.equals("Unknown")) {
                return classPath;
            }
            // 如果在当前模块中定义的类
            return currentModulePath + "." + objectPart;
        }

        // 检查是否是变量名，尝试从作用域解析
        String variableType = scopeManager.resolveSymbol(objectPart, currentLine);
        if (variableType != null && !variableType.equals("Unknown")) {
            return variableType;
        }
        
        return "Unknown";
    }
    
    /**
     * 检测原生类型字面量
     */
    private static String detectBuiltinTypeLiteral(String objectPart) {
        if (objectPart == null || objectPart.trim().isEmpty()) {
            return null;
        }

        objectPart = objectPart.trim();

        // 字符串字面量
        if ((objectPart.startsWith("\"") && objectPart.endsWith("\"")) ||
            (objectPart.startsWith("'") && objectPart.endsWith("'"))) {
            return "str";
        }

        // 字典字面量 - 更精确的检测
        if (objectPart.startsWith("{") && objectPart.endsWith("}") && objectPart.contains(":")) {
            return "dict";
        }

        // 集合字面量 - 更精确的检测
        if (objectPart.startsWith("{") && objectPart.endsWith("}") && !objectPart.contains(":") && objectPart.length() > 2) {
            return "set";
        }

        // 元组字面量 - 更精确的检测
        if (objectPart.startsWith("(") && objectPart.endsWith(")") && objectPart.contains(",")) {
            return "tuple";
        }

        // 列表字面量
        if (objectPart.startsWith("[") && objectPart.endsWith("]")) {
            return "list";
        }

        // 布尔字面量
        if ("True".equals(objectPart) || "False".equals(objectPart)) {
            return "bool";
        }

        // 数字字面量
        if (Pattern.matches("\\d+", objectPart) || Pattern.matches("\\d+\\.\\d+", objectPart)) {
            if (objectPart.contains(".")) {
                return "float";
            } else {
                return "int";
            }
        }

        return null;
    }
    
    /**
     * 检查是否是内置函数
     */
    private static boolean isBuiltinFunction(String functionName) {
        // Python内置函数列表
        String[] builtinFunctions = {
            "abs", "all", "any", "ascii", "bin", "bool", "bytearray", "bytes",
            "callable", "chr", "classmethod", "compile", "complex", "delattr",
            "dict", "dir", "divmod", "enumerate", "eval", "exec", "filter",
            "float", "format", "frozenset", "getattr", "globals", "hasattr",
            "hash", "help", "hex", "id", "input", "int", "isinstance",
            "issubclass", "iter", "len", "list", "locals", "map", "max",
            "memoryview", "min", "next", "object", "oct", "open", "ord",
            "pow", "print", "property", "range", "repr", "reversed", "round",
            "set", "setattr", "slice", "sorted", "staticmethod", "str", "sum",
            "super", "tuple", "type", "vars", "zip", "__import__"
        };
        
        for (String builtin : builtinFunctions) {
            if (builtin.equals(functionName)) {
                return true;
            }
        }
        return false;
    }
}
