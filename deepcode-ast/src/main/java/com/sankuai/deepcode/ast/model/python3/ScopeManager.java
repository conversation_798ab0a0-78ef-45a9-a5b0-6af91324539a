package com.sankuai.deepcode.ast.model.python3;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

@Getter
public class ScopeManager {
    public static final String UNKNOWN = "Unknown";
    /**
     * 整个模块下，作用域的映射关系
     * key：模块路径
     * value：作用域下的映射关系
     * --> key：作用域下，变量名称
     * --> value：变量全路径
     */
    private final Map<String, Map<String, TreeMap<Integer, VariableInfo>>> scopes = Maps.newConcurrentMap();
    private String currentScope = "";
    /**
     * 内置函数列表
     */
    private final static Set<String> BUILTIN_FUNCTIONS = Sets.newHashSet(
            // 1. 类型转换
            "int", "float", "str", "bool", "list", "tuple", "dict", "set", "bytes", "ord", "chr", "hex", "bin", "oct", "ascii", "hash", "bytearray",
            // 2. 输入、输出
            "print", "input",
            // 3. 数学函数
            "abs", "round", "max", "min", "sum", "pow", "divmod", "complex",
            // 4. 序列操作函数
            "len", "sorted", "reversed", "all", "any", "enumerate", "filter", "map", "zip", "next", "iter", "frozenset", "slice",
            // 5. 对象和属性操作函数
            "type", "isinstance", "hasattr", "getattr", "setattr", "delattr", "vars", "super", "memoryview",
            // 6. 其他常用函数
            "id", "dir", "help", "callable", "eval", "exec", "open", "range", "map", "filter", "repr", "format", "compile", "breakpoint", "globals", "locals",
            // 7. 内置装饰器
            "staticmethod", "classmethod", "property"
    );

    private final static Set<String> BUILTIN_EXCEPTIONS = Sets.newHashSet(
            "BaseException", "Exception", "ArithmeticError", "BufferError", "LookupError",
            "AssertionError", "AttributeError", "EOFError", "FloatingPointError",
            "GeneratorExit", "ImportError", "ModuleNotFoundError", "IndexError",
            "KeyError", "KeyboardInterrupt", "MemoryError", "NameError",
            "NotImplementedError", "OSError", "OverflowError", "RecursionError",
            "ReferenceError", "RuntimeError", "StopIteration", "StopAsyncIteration",
            "SyntaxError", "IndentationError", "TabError", "SystemError", "SystemExit",
            "TypeError", "UnboundLocalError", "UnicodeError", "UnicodeEncodeError",
            "UnicodeDecodeError", "UnicodeTranslateError", "ValueError", "ZeroDivisionError"
    );


    public ScopeManager() {
        scopes.put("", Maps.newConcurrentMap()); // 全局作用域
    }

    public ScopeManager(String moduleName) {
        this.currentScope = moduleName;
        scopes.put(moduleName, Maps.newConcurrentMap()); // 模块下的全局作用域
    }

    public void enterScope(String name) {
        currentScope = currentScope.isEmpty() ? name : currentScope + "." + name;
        scopes.putIfAbsent(currentScope, Maps.newConcurrentMap());
    }

    public void exitScope() {
        int lastDotIndex = currentScope.lastIndexOf('.');
        currentScope = lastDotIndex == -1 ? currentScope : currentScope.substring(0, lastDotIndex);
    }

    /**
     * 添加到作用域
     *
     * @param name                 添加的变量名称
     * @param fullSourceModulePath 变量全路径【需要自行处理全路径】
     */
    public void addToScope(String name, String fullSourceModulePath, int line) {
        if (name == null) {
            return;
        }
        Map<String, TreeMap<Integer, VariableInfo>> scopeMap = scopes.get(currentScope);
        scopeMap.putIfAbsent(name, Maps.newTreeMap());
        scopeMap.get(name).put(line, new VariableInfo(line, fullSourceModulePath));
    }

    public String resolveSymbol(String name, int line) {
        if (name == null) {
            return UNKNOWN;
        }
        String scope = currentScope;
        boolean isCurrentScope = true;

        while (true) {
            Map<String, TreeMap<Integer, VariableInfo>> currentScopeMap = scopes.get(scope);
            if (currentScopeMap.containsKey(name)) {
                TreeMap<Integer, VariableInfo> lineMap = currentScopeMap.get(name);
                if (isCurrentScope) {
                    // 在当前作用域中，查找最近的定义
                    Map.Entry<Integer, VariableInfo> entry = lineMap.floorEntry(line);
                    if (entry != null) {
                        return entry.getValue().fullPath;
                    }
                } else {
                    // 在父级作用域中，使用最后一次定义
                    return lineMap.lastEntry().getValue().fullPath;
                }
            }

            int lastDotIndex = scope.lastIndexOf('.');
            if (lastDotIndex == -1) {
                break;
            }
            scope = scope.substring(0, lastDotIndex);
            isCurrentScope = false;
        }

        // 检查全局作用域
        Map<String, TreeMap<Integer, VariableInfo>> globalScopeMap = scopes.get("");
        if (globalScopeMap != null && globalScopeMap.containsKey(name)) {
            return globalScopeMap.get(name).lastEntry().getValue().fullPath;
        }

        if (isBuiltinFunction(name)) {
            return "BUILTIN." + name;
        } else if (isBuiltinException(name)) {
            return "BUILTIN_EXCEPTION." + name;
        }

        return UNKNOWN;
    }

    public void addImport(String name, String fullPath, int line) {
        addToScope(name, fullPath, line);
    }

    public void addAlias(String alias, String original, int line) {
        addToScope(alias, original, line);
        /*
        String resolvedOriginal = resolveSymbol(original);
        if (resolvedOriginal != null) {
            addToScope(alias, resolvedOriginal);
        } else {
            addToScope(alias, original); // 如果无法解析原始符号，则使用原始名称
        }
         */
    }

    public void removeFromScope(String name) {
        scopes.get(currentScope).remove(name);
    }

    public boolean isInCurrentScope(String name, int line) {
        Map<String, TreeMap<Integer, VariableInfo>> scopeMap = scopes.get(currentScope);
        if (scopeMap.containsKey(name)) {
            TreeMap<Integer, VariableInfo> lineMap = scopeMap.get(name);
            return lineMap.floorKey(line) != null;
        }
        return false;
    }

    public static boolean isBuiltinFunction(String name) {
        return BUILTIN_FUNCTIONS.contains(name);
    }

    public static boolean isBuiltinException(String name) {
        return BUILTIN_EXCEPTIONS.contains(name);
    }

    /**
     * 获取所有作用域的 keySet
     *
     * @return 所有作用域名称的集合
     */
    public Set<String> getScopeKeySet() {
        return scopes.keySet();
    }

    /**
     * 获取指定作用域下的所有变量名称
     *
     * @param scopeName 作用域名称
     * @return 该作用域下的所有变量名称集合
     */
    public Set<String> getVariableNamesInScope(String scopeName) {
        Map<String, TreeMap<Integer, VariableInfo>> scopeMap = scopes.get(scopeName);
        return scopeMap != null ? scopeMap.keySet() : Sets.newHashSet();
    }

    /**
     * 获取指定作用域下的所有变量信息
     *
     * @param scopeName 作用域名称
     * @return 该作用域下的所有变量信息映射
     */
    public Map<String, TreeMap<Integer, VariableInfo>> getScopeVariables(String scopeName) {
        return scopes.get(scopeName);
    }

    /**
     * 获取所有作用域的完整映射
     *
     * @return 所有作用域的映射关系
     */
    public Map<String, Map<String, TreeMap<Integer, VariableInfo>>> getAllScopes() {
        return Maps.newHashMap(scopes);
    }

    /**
     * 将另一个 ScopeManager 的所有符号添加到当前作用域
     * 用于处理 "from xxx import *" 的情况
     *
     * @param sourceScopeManager 源 ScopeManager
     * @param sourceScope 源作用域名称
     * @param targetLine 目标行号
     */
    public void importAllSymbolsFromScope(ScopeManager sourceScopeManager, String sourceScope, int targetLine) {
        if (sourceScopeManager == null || sourceScope == null) {
            return;
        }

        Map<String, TreeMap<Integer, VariableInfo>> sourceScopeMap = sourceScopeManager.getScopeVariables(sourceScope);
        if (sourceScopeMap == null) {
            return;
        }

        // 将源作用域的所有变量添加到当前作用域
        for (Map.Entry<String, TreeMap<Integer, VariableInfo>> entry : sourceScopeMap.entrySet()) {
            String variableName = entry.getKey();
            TreeMap<Integer, VariableInfo> lineMap = entry.getValue();

            // 获取最新的变量定义
            if (!lineMap.isEmpty()) {
                VariableInfo latestInfo = lineMap.lastEntry().getValue();
                addToScope(variableName, latestInfo.fullPath, targetLine);
            }
        }
    }

    /**
     * 获取当前作用域名称
     *
     * @return 当前作用域名称
     */
    public String getCurrentScope() {
        return currentScope;
    }

    /**
     * 检查是否存在指定的作用域
     *
     * @param scopeName 作用域名称
     * @return 是否存在该作用域
     */
    public boolean hasScopeNamed(String scopeName) {
        return scopes.containsKey(scopeName);
    }

    @Getter
    @Setter
    @ToString
    private static class VariableInfo {
        int line;
        String fullPath;

        VariableInfo(int line, String fullPath) {
            this.line = line;
            this.fullPath = fullPath;
        }
    }

}
