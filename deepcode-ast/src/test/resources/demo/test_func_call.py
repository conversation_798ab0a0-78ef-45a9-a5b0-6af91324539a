import os
from typing import Optional, List

from dotenv import load_dotenv


def get_from_env(name, default: Optional[str] = None) -> str:
    if name in os.environ and (r := os.environ[name]):
        return r
    elif default is not None:
        return default
    else:
        raise ValueError(
            f"Did not find {name}, please add an environment variable or config in .env file"
        )


def get_list_from_env(name, separator: str = ',', default: Optional[List[str]] = None) -> List[str]:
    list_str = get_from_env(name, "")
    if not list_str:
        if default is None:
            default = []
        return default
    return [_.strip() for _ in list_str.split(separator) if _]


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

env_map = {
    "LOCAL": "local.env",
    "TEST": "test.env"
}

env = os.environ.get("ENV") or "LOCAL"
env_file = env_map.get(env, "local.env")
load_dotenv(os.path.join(BASE_DIR, env_file))


def is_local():
    return env == "LOCAL"


def is_test():
    return env == "TEST"


def is_write_log_file():
    return get_from_env("WRITE_LOG_FILE") == "True"


APPKEY = get_from_env("APPKEY")

LOG_IGNORE_URIS = get_list_from_env("LOG_IGNORE_URI")